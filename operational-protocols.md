# Operational Protocols

Defines specific, high-level commands that can be invoked during a session to perform routine but critical workflows.

## Command: "Let's Review"

**Triggers:**
- Let's review
- Let us review
- Let's pick up where we left off

**Objective:** Efficiently and safely resume work by establishing a shared understanding of the project’s current state and verifying its stability.

### Phases

#### Phase 1: Context Synthesis
- **Parse `README.md`** – Identify the project’s overall purpose and core features. *(Required)*
- **Parse `dev_notes.md`** – Identify most recent work, next steps, and blockers. *(Required)*
- **Parse `project_plan.md`** – Align tasks with roadmap. *(Optional)*
- **Report Context Summary**

```
**Project Context Review:**

* **Project Goal:** [Summary from README.md]  
* **Current Status:** [Summary from dev_notes.md]  
* **Next Objective:** [Summary of next steps from dev_notes.md and project_plan.md]
```

#### Phase 2: Stability Verification
- **Announce Verification Plan**
- **Execute Test Verification**
  - **Conceptual Review (Default):** Read test files, check for skips or incomplete tests.
  - **Execution (Preferred):** Run test suite and capture results.
- **Report Stability Summary**

```
**Code Stability Review:**

* **Test Suite Status:** OK | ATTENTION_NEEDED | FAILING  
* **Summary:** [e.g., Reviewed 25 tests across 4 files.]  
* **Action Items:** [List of failing or incomplete tests.]
```

#### Phase 3: Readiness Confirmation
- **Declare System Status**
  - ✅ **System Ready** – Context is synchronized and codebase is stable.
  - ⚠️ **Attention Required** – Issues detected. Include a brief summary.

---

## Command: "Finalize and Clean Up"

**Triggers:**
- Okay, that’s working now. Let’s clean up.
- The fix is confirmed. Please finalize the changes.
- The feature is now working as expected.

**Objective:** Integrate a successful solution, sanitize the codebase, and document key learnings.

### Phases

#### Phase 1: Knowledge Distillation & Documentation
- **Identify Learning Artifacts:** Review scripts, comments, and notes
- **Synthesize the Core Lesson** – Structure: Problem, Root Cause, Solution, Takeaway
- **Update `knowledge_base.md`**

```
---
### [YYYY-MM-DD]: [Brief Description of Problem]

* **Problem:** ...  
* **Root Cause:** ...  
* **Solution:** ...  
* **Key Takeaway:** ...
```

- **Update Code-Level Docs** – Add `Note:` or `Gotcha:` to docstrings if applicable

#### Phase 2: Codebase Sanitization & Integration
- **Convert Debug Script to Regression Test**
- **Remove Temporary Artifacts** – Clean `/scripts`
- **Sanitize Production Code** – Remove prints, temp logs, commented blocks
- **Final Linting & Formatting** – Run `ruff`, `black`, or equivalent
- **Propose Final Commit** – Create an atomic commit with clear description

---

## Command: "Walk Me Through Recent Changes"

**Triggers:**
- Walk me through the recent changes.
- Can you give me a recap of the latest commits?
- What's new on this branch?

**Objective:** Provide a concise summary of recent changes with purpose and impact.

### Methodology

1. **Determine Scope** – Ask user, default to last 5 commits
2. **Fetch Git History** – Use `git log` to pull commit details
3. **Synthesize Chronologically** – Focus on the *why*, not just the *what*
4. **Present Walkthrough**

```
### 🚀 Walkthrough of Recent Changes

*Summarizing [X] commits from [start_date] to [end_date].*

---

**Commit 1/X: `feat(auth): add password reset flow`**
* **Author:** anthonykalinde  
* **SHA:** a1b2c3d  
* **Impact:** Adds password reset flow allowing users to recover access

**Commit 2/X: `refactor(services): simplify Cognee API client`**
* **Author:** AI Partner  
* **SHA:** b4e5f6g  
* **Impact:** Reduces duplication, improves error handling

**Commit 3/X: `fix(ui): correct button alignment on login page`**
* **Author:** anthonykalinde  
* **SHA:** h7i8j9k  
* **Impact:** Fixes visual bug on smaller screens

---

**Overall Summary:**
Introduced password reset functionality, improved maintainability, and addressed minor UI bug.
```

---

# Research & Information Retrieval Protocol

Defines the mandatory strategy for researching unknown problems, errors, or concepts.

## Objective
To find accurate, relevant information and synthesize answers with source attribution.

### Methodology

1. **Formulate a Precise Query**
   - Use specific error messages or keywords.
   - ❌ "Python import problem"  
   - ✅ "ImportError: attempted relative import beyond top-level package python"

2. **Search by Source Hierarchy**
   - Consult sources in priority order. Don’t skip.

3. **Synthesize and Cite**
   - Don’t just dump links. Explain and attribute sources.
   - _Example_: “According to the official `httpx` documentation…”

4. **Report Failure and Refine**
   - If nothing found, report what was searched and ask for alternatives.

### Source Hierarchy

#### Priority 1: Internal Codebase (GitHub)
- Look for implementations, utility functions, usage examples.

#### Priority 2: Project-Specific Knowledge Base
- Domain knowledge, architectural decisions, acronyms.

#### Priority 3: External Knowledge (Brave Search)
- **Search Order:**
  1. Official Docs (e.g., python.org, starlette.io)
  2. Stack Overflow (voted & accepted answers)
  3. GitHub Issues for relevant libs
  4. Technical Blogs (Real Python, etc.)
  5. Forums (Reddit, only with verification)