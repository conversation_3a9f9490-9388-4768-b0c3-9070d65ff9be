# GitHub Copilot Prompt Engineering Repository

A comprehensive collection of optimized GitHub Copilot instructions and prompt engineering resources designed to enhance code generation quality and developer productivity.

## 🚀 Quick Start

### For New Projects
1. **Choose a template** from `restructured-prompts/templates/`
2. **Copy to your project**: Place the template content in `.github/copilot-instructions.md`
3. **Customize**: Modify the template to match your specific project needs

### For Workspace-Level Setup
1. **Copy workspace files** from `restructured-prompts/workspace-level/`
2. **Apply globally**: Use these as organization or personal workspace instructions
3. **Combine with repository-level**: These work alongside project-specific instructions

## 📁 Repository Structure

```
copilot-prompts/
├── restructured-prompts/          # ✨ Optimized Copilot content
│   ├── workspace-level/           # General practices (any repository)
│   ├── repository-level/          # Technology-specific instructions
│   ├── templates/                 # Ready-to-use copilot-instructions.md
│   └── examples/                  # Patterns and before/after comparisons
├── archive/                       # 📦 Original content (preserved for reference)
└── README.md                      # This file
```

## 🎯 What's Inside

### 📋 Templates (Ready to Use)
- **`python-project-template.md`** - FastAPI, async/await, modern Python tooling
- **`flutter-project-template.md`** - Riverpod, go_router, Dart 3.0+ features
- **`general-project-template.md`** - Universal principles for any technology stack

### 🌐 Workspace-Level Instructions
- **`core-engineering-principles.md`** - Fundamental software engineering values
- **`problem-solving-methodology.md`** - Systematic debugging and investigation
- **`cognitive-framework.md`** - Decision-making and mental models
- **`code-quality-principles.md`** - Architecture and design principles
- **`testing-philosophy.md`** - Universal testing strategies

### 🔧 Repository-Level Instructions
- **`python-development.md`** - Python 3.12+, uv, ruff, pytest, FastAPI
- **`flutter-dart-development.md`** - Flutter 3.10+, Riverpod 2.0+, modern Dart
- **`python-testing.md`** - pytest patterns, mocking, async testing
- **`flutter-testing.md`** - Widget tests, integration tests, Riverpod testing

### 📚 Examples and Patterns
- **`before-after/`** - Transformations from ineffective to effective prompts
- **`common-patterns/`** - Reusable techniques and anti-patterns
- **`real-world-scenarios/`** - Practical implementation examples

## 🛠️ Implementation Guide

### Step 1: Choose Your Approach

#### Option A: Use a Template (Recommended for new projects)
```bash
# Copy the appropriate template to your project
cp restructured-prompts/templates/python-project-template.md .github/copilot-instructions.md
```

#### Option B: Build Custom Instructions
1. Start with workspace-level principles
2. Add relevant repository-level guidance
3. Customize for your specific needs

### Step 2: Customize for Your Project
- **Technology Stack**: Update versions and tool preferences
- **Coding Standards**: Add project-specific naming conventions
- **Architecture Patterns**: Include your preferred design patterns
- **Testing Strategy**: Specify your testing requirements

### Step 3: Validate and Iterate
- **Length Check**: Keep under 150 lines for optimal Copilot processing
- **Specificity**: Include concrete examples rather than abstract principles
- **ApplyTo Directives**: Use file pattern matching for targeted guidance

## 🎨 Key Features

### ✅ Copilot-Optimized
- **Concise**: All files under 150 lines for optimal context processing
- **Specific**: Concrete examples and patterns rather than abstract theory
- **Targeted**: Proper `applyTo` directives for file-specific guidance

### ✅ Modern Technology Stacks
- **Python**: 3.12+, uv, ruff, FastAPI, async/await patterns
- **Flutter**: 3.10+, Riverpod 2.0+, go_router, modern Dart features
- **Universal**: Principles that apply across all technology stacks

### ✅ Comprehensive Coverage
- **Development**: Coding standards, architecture patterns, best practices
- **Testing**: Unit, integration, and E2E testing strategies
- **Quality**: Error handling, performance, security considerations

## 📖 Usage Examples

### Python FastAPI Project
```python
# Copilot will generate code following these patterns:

async def get_user(user_id: int, db: AsyncSession) -> User | None:
    """Get user by ID with proper error handling."""
    try:
        return await db.get(User, user_id)
    except SQLAlchemyError as e:
        logger.error("Database error fetching user", extra={"user_id": user_id, "error": str(e)})
        raise DatabaseError(f"Failed to fetch user {user_id}") from e
```

### Flutter Riverpod State Management
```dart
// Copilot will generate providers following these patterns:

@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  Future<User?> build() async {
    return ref.read(userRepositoryProvider).getCurrentUser();
  }

  Future<void> updateUser(User user) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return ref.read(userRepositoryProvider).updateUser(user);
    });
  }
}
```

## 🔄 Migration from Original Structure

The original content has been preserved in the `archive/` directory. The restructured content:

- **Reduces length** by 60-80% while preserving essential information
- **Adds concrete examples** to replace abstract discussions
- **Implements proper `applyTo` directives** for targeted application
- **Separates concerns** between workspace and repository-level guidance

## 🤝 Contributing

1. **Follow the established patterns** when adding new content
2. **Keep files under 150 lines** for optimal Copilot processing
3. **Include concrete examples** rather than abstract principles
4. **Test with real projects** to validate effectiveness

## 📄 License

This repository is open source and available under the [MIT License](LICENSE).

## 🔗 Related Resources

- [GitHub Copilot Documentation](https://docs.github.com/en/copilot)
- [Custom Instructions Guide](https://docs.github.com/en/copilot/how-tos/custom-instructions)
- [Prompt Engineering Best Practices](https://docs.github.com/en/copilot/concepts/prompt-engineering-for-copilot-chat)

---

**Made with ❤️ for better AI-assisted development**
