---
applyTo: "**/*.py"
---

State-of-the-Art Python Development: The <PERSON> Edition
1. Core Philosophy: The Zen of Python
All our development practices are guided by the principles laid out in the Zen of Python (import this). This is not just a collection of aphorisms; it is the constitution for how we write code.

Beautiful is better than ugly.

Explicit is better than implicit.

Simple is better than complex.

Readability counts.

Our central tenet, inspired by <PERSON>, is this: Code is read far more often than it is written. Our primary goal is to produce code that is clear, expressive, and easily maintainable by our future selves and other developers. If a choice must be made between a "clever" solution and a clear one, we will always choose clarity.

2. Technology Stack (2025 Edition)
To ensure consistency, performance, and a modern development experience, we will use the following state-of-the-art toolchain.

Language: Python 3.12+

Why: To leverage the latest performance improvements, modern asyncio features, and language enhancements like structural pattern matching.

Dependency & Environment Management: uv

Why: It is an extremely fast, modern tool that serves as both a package installer and virtual environment manager. It simplifies the development workflow and significantly speeds up dependency resolution, replacing the need for separate pip and venv commands.

Linting & Formatting: Ruff

Why: Ruff is an all-in-one, extremely fast tool written in Rust. It replaces a collection of older tools like Flake8, isort, and Black. We will use Ruff for linting, formatting, and import sorting to ensure a single, consistent standard for code quality.

Testing Framework: pytest

Why: Its simple, expressive syntax using plain assert statements, powerful fixture system, and rich plugin ecosystem make it the undisputed standard for Python testing.

Data Validation & Settings Management: Pydantic

Why: It uses Python's type hints to provide runtime data validation, serialization, and settings management. It is the gold standard for building robust, data-aware applications, especially APIs.

Web Framework (for APIs): FastAPI

Why: Built on top of Pydantic and Starlette, FastAPI provides automatic interactive documentation, best-in-class performance, and a development experience that fully leverages modern type hints.

3. Project Structure: The Modern src Layout
To ensure a clean separation between our application code and project configuration files, we will use the src layout.

project-root/
├── .venv/                     # Virtual environment managed by uv
├── src/                       # Main application source code
│   └── my_project/
│       ├── __init__.py
│       ├── api/               # API endpoints (FastAPI routers)
│       ├── core/              # Core logic, settings, etc.
│       ├── models/            # Pydantic data models
│       └── services/          # Business logic
├── tests/                     # All tests
│   ├── __init__.py
│   ├── test_api.py
│   └── test_services.py
├── .env                       # Environment variables (never committed)
├── .gitignore
├── pyproject.toml             # Project definition, dependencies, and tool config
└── README.md

Why the src layout?

It prevents accidental imports of your package from the current working directory, which can cause subtle bugs.

It ensures your package is installed correctly and runs from its installed location, just as it would in production.

It creates a clean project root, separating Python source code from documentation, tests, and configuration.

4. Development Guidelines: Writing Pythonic Code
Style and Formatting
The Rule: All code must be formatted using Ruff.

Implementation: The pyproject.toml file will be configured to run ruff format . and ruff check --fix .. This is non-negotiable and ensures perfect, consistent PEP 8 compliance without manual effort.

Type Hints
The Rule: All new functions and methods must have 100% type annotation for their arguments and return values.

Why: Type hints are not just for documentation. They are a critical tool for static analysis (mypy), editor support (autocompletion), and preventing an entire class of runtime bugs.

Example:

# Clear, explicit, and tool-friendly
def get_user(user_id: int, db_session: DbSession) -> User | None:
    # ... implementation ...

Asynchronous Code (asyncio)
The Rule: Use async / await for all I/O-bound operations. This includes database calls, external API requests, and reading/writing from files if performance is critical.

Why: asyncio allows the application to handle thousands of concurrent I/O operations without blocking, making it essential for building high-performance network services.

Guidance: Do not mix async and synchronous code carelessly. An async function should ideally only call other async functions or I/O libraries that support it.

Structured Logging
The Rule: Use Python's built-in logging module. Do not use print() statements for debugging or application output.

Why: The logging module allows for configurable log levels (DEBUG, INFO, WARNING, ERROR), routing output to different destinations (console, files, external services), and adding rich contextual information to logs, which is invaluable in production.

5. Testing Doctrine: Trustworthy and Maintainable Tests
Our testing philosophy is guided by the principle that a test suite is a living specification of the system's behavior.

Test Runner: pytest is the only test runner to be used.

Test-Driven Development (TDD): For all new features, follow the "Red-Green-Refactor" cycle. Write a failing test first, then write the code to make it pass, and finally refactor both code and test for clarity.

Mocking: Use the pytest-mock plugin for mocking external dependencies (like APIs or database sessions) in unit tests. Mocks ensure that tests are fast and isolated.

Structure:

Unit Tests: Test a single function or class in isolation. All dependencies must be mocked.

Integration Tests: Test the interaction between a few components (e.g., an API endpoint calling a service that uses a Pydantic model). Mock only genuinely external systems.

Clarity: A test should be so clear that its purpose is obvious from reading its name and its assert statements.

def test_inactive_user_cannot_login():
    # ... setup ...
    assert result.status_code == 401
    assert result.json()["detail"] == "User is inactive"

6. Dependency & Environment Management: Speed and Sanity
The Rule: All project dependencies—both for production and development—must be managed in pyproject.toml.

Tool: uv is the standard tool for all dependency and environment operations.

Common Workflow
Create an environment:

uv venv

Activate the environment:

source .venv/bin/activate

Add a new dependency:

uv pip install "fastapi"

Add a new development dependency:

uv pip install "pytest" --group dev

Install all dependencies from pyproject.toml:

uv pip install -e .[dev]

Guido's Blessing
Ultimately, these rules serve one purpose: to help us build better software together. When faced with a situation not covered by these guidelines, fall back on the Zen of Python. Ask yourself: "Which approach is more readable? Which is simpler? Which is more explicit?" The answer to those questions is the Pythonic way—and the path we will follow.