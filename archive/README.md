# Archive - Original Copilot Prompts

This directory contains the original content from the repository before restructuring for optimal GitHub Copilot usage.

## Archived Files

### Original Prompt Files
- `core-principles.md` - AI Software Development Partner principles and responsibilities
- `python-instruction-set.md` - State-of-the-art Python development guidelines
- `flutter-stack.md` - Dart & Flutter technology stack instructions
- `testing-philosophy.md` - Comprehensive testing philosophy and strategy
- `quality-assurance.md` - Quality attributes framework and processes
- `cognitive-framework.md` - Decision making and mental models for development
- `code-quality-principals.md` - Code quality and architecture principles
- `problem-solving-methodolgy.md` - Problem-solving and debugging methodology
- `operational-protocols.md` - High-level operational commands and workflows
- `interaction-principles.md` - AI interaction guidelines and structure
- `custom.md` - Master index file with include directives

### Legacy Content
- `old-prompts/` - Previous versions of Python development prompts
  - `python-dev-v1.2.yaml`
  - `python-dev-v13.yaml`
  - `python-dev-v14.yaml`

## Why These Were Archived

The original files contained excellent content but had several issues for GitHub Copilot usage:

1. **Length**: Many files exceeded 200+ lines, overwhelming Copilot's context processing
2. **Structure**: Designed for general AI assistants rather than Copilot's specific capabilities
3. **Organization**: Mixed abstraction levels and audiences in single files
4. **Format**: Lacked proper `applyTo` directives for targeted application

## Content Preservation

All valuable content from these files has been:
- **Preserved**: Core principles and practices maintained
- **Optimized**: Restructured for Copilot's processing capabilities
- **Categorized**: Separated into workspace-level and repository-level guidance
- **Modernized**: Updated for current Copilot features and best practices

## Migration Mapping

The restructured content can be found in:
- `../restructured-prompts/workspace-level/` - General principles and methodologies
- `../restructured-prompts/repository-level/` - Technology-specific guidance
- `../restructured-prompts/templates/` - Ready-to-use Copilot instruction templates
- `../restructured-prompts/examples/` - Practical examples and patterns

## Reference Usage

These archived files remain valuable for:
- Understanding the evolution of the prompt engineering approach
- Referencing comprehensive details not included in the optimized versions
- Historical context for decision-making processes
- Detailed explanations of principles summarized in the restructured content

