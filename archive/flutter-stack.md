---
applyTo: "**/*.dart"
---

# Stack-Specific Instruction Set: Dart & Flutter
# Version: 1.0
# Last Updated: 2025-06-12
# Description: This file provides the technology-specific implementation details for a Dart & Flutter project.
# It is designed to be used in conjunction with the core_engineering_philosophy.yaml file.

# ---
# The core technologies used in this project.
technology_stack:
  - name: "Dart"
    version: "3.0+"
    notes: "Utilize modern features like records, patterns, and class modifiers."
  - name: "Flutter"
    version: "3.10+"
    purpose: "Cross-platform UI toolkit for building the application frontend."
  - name: "Riverpod"
    version: "2.0+"
    purpose: "The primary framework for dependency injection and state management. Prefer this over Provider or BLoC."
  - name: "Dio"
    purpose: "The primary HTTP client for all external network requests. Prefer over the base `http` package."
  - name: "go_router"
    purpose: "Declarative routing solution to manage navigation and deep linking."
  - name: "flutter_test / test"
    purpose: "The standard frameworks for unit and widget testing."
  - name: "integration_test"
    purpose: "Framework for End-to-End (E2E) testing on a device or emulator."
  - name: "mocktail"
    purpose: "A powerful and type-safe mocking library for tests. Prefer over Mockito."
  - name: "Melos"
    purpose: "(Optional, for larger projects) A tool for managing multi-package Dart/Flutter monorepos."

# ---
# Rules defining the required project structure and file layout.
project_structure:
  description: "A clean, feature-first directory layout to ensure separation of concerns and scalability."
  layout: |
    /lib
      /src
        /features              # Main application features (e.g., auth, settings, dashboard)
          /auth
            /application         # State management logic (Riverpod providers)
            /domain              # Business logic and entities (pure Dart)
            /data                # Data sources (API clients, repositories)
            /presentation        # UI (Widgets, Screens, Components)
              /screens
              /widgets
        /core                  # Shared, cross-cutting concerns
          /api                   # API client setup (e.g., Dio instance)
          /config                # App configuration, themes, constants
          /routing               # GoRouter setup and route definitions
          /utils                 # Utility functions
          /widgets               # Common, shared widgets (e.g., AppButton)
      main.dart                # App entry point and ProviderScope setup
    /test
      /features
        /auth
          /application
          /domain
          /data
      # ... (test structure mirrors lib/src)
    pubspec.yaml               # Project dependencies and metadata
    analysis_options.yaml      # Linter rules and static analysis configuration

# ---
# Core development principles and coding standards specific to Dart & Flutter.
development_guidelines:
  dart:
    - "Enable and adhere to all rules in the `flutter_lints` package. Customize strict rules in `analysis_options.yaml`."
    - "Always use `final` for variables that are not reassigned. Use `const` wherever possible, especially for constructors and widgets to improve performance."
    - "Utilize Dart's null safety features rigorously. Avoid using the `!` (bang) operator."
    - "Leverage pattern matching and records for function returns and destructuring to improve readability."
  flutter:
    - "Build the UI declaratively. State should be managed by Riverpod providers, and widgets should react to state changes."
    - "Break down complex screens into smaller, reusable widgets. A widget should ideally fit on one screen and have a single responsibility."
    - "Prefer `const` constructors for widgets to allow Flutter to skip unnecessary rebuilds."
    - "Use `go_router` for all navigation. Avoid using the imperative `Navigator.of(context).push()`."
  state_management:
    - "Use Riverpod for all state management. For simple state, use `StateProvider`. For complex logic, use `Notifier` or `AsyncNotifier` from `riverpod_generator`."
    - "Data fetching and business logic must be handled within Riverpod providers, not directly in the widget build methods."
  api_integration:
    - "Centralize all API calls within Repository classes in the `/data` layer of each feature."
    - "Use `Dio` for network requests. Configure a single `Dio` instance with interceptors for logging, authentication, and error handling."
    - "Model all API responses using pure Dart classes with `fromJson`/`toJson` methods. Consider using a generator like `freezed` or `json_serializable` for this."

# ---
# Implementation of the testing philosophy for the Flutter stack.
testing_implementation:
  testing_pyramid_strategy:
    levels:
      - level: "1. Unit Tests (Dart)"
        tool: "test"
        location: "/test/**"
        description: "Tests a single function, class, or Riverpod Notifier in complete isolation. All external dependencies must be mocked using `mocktail`."
      - level: "2. Widget Tests (Flutter)"
        tool: "flutter_test"
        location: "/test/**"
        description: "Tests a single widget in isolation. Verifies UI appearance and interaction. Use `flutter_test`'s `pumpWidget` and finder methods. Mock service/provider dependencies."
      - level: "3. Integration / E2E Tests (Flutter)"
        tool: "integration_test"
        location: "/integration_test/"
        description: "Tests a complete user workflow on a real device or emulator. The test script drives the app through its UI to verify feature behavior from end to end."
  test_organization:
    - rule: "Mirrored Structure"
      instruction: "The directory structure within `/test` must mirror the application's structure in `/lib/src`."
    - rule: "Test File Naming"
      instruction: "All test files must end with the `_test.dart` suffix."
    - rule: "Clear Naming"
      instruction: "Test descriptions should read like a sentence describing the behavior (e.g., `test('tapping button increments counter', ...)`)."
