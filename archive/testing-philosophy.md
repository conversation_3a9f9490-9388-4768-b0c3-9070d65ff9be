---
applyTo: "**/tests/**"
---

# Section 2: Testing Philosophy & Strategy

## Core Testing Philosophy

**Objective:** Create a comprehensive suite of executable specifications that validate all critical system behaviors, providing confidence in changes and serving as living documentation of system requirements.

**Central Tenet:** Tests are not just verification tools—they are executable specifications that define expected system behavior and serve as a safety net for continuous evolution.

## The Four Pillars of Testing Excellence

### 1. The Golden Rule of Test Integrity
**Principle:** Application code changes to make tests pass. Tests never change to accommodate faulty application code.

**Application:**
- A failing test indicates a problem in the application logic, not the test specification
- When tests fail after code changes, first assume the application code is incorrect
- Only modify tests when requirements have genuinely changed, not when implementation details shift
- Treat test failures as sacred signals that must be investigated, not ignored

### 2. Tests as Executable Documentation
**Principle:** Every test must clearly communicate a specific behavioral requirement through its structure, naming, and assertions.

**Application:**
- Test names should read like specifications: `should_calculate_correct_discount_when_coupon_is_valid`
- Use the Given-When-Then pattern to structure test logic clearly
- Focus on testing behaviors and outcomes, not implementation details
- Write tests that future developers can read to understand system requirements

### 3. Comprehensive Behavioral Coverage
**Principle:** Achieve 100% coverage of critical user journeys and business logic, not necessarily 100% code coverage.

**Application:**
- Every user story or business requirement must have corresponding test coverage
- Prioritize testing critical paths and edge cases over comprehensive code coverage
- Use risk-based testing to focus effort on high-impact, high-probability failure scenarios
- Document any deliberately untested code paths with clear justification

### 4. Strategic Test Distribution
**Principle:** Apply the testing pyramid to optimize for both speed and confidence in the test suite.

**Testing Pyramid Strategy:**
```
    /\     E2E Tests (10%)
   /  \    Slow, comprehensive, high confidence
  /____\   Test complete user workflows
 /      \  
/        \  Integration Tests (20%)
\        /  Medium speed, moderate scope
 \______/   Test component interactions
/        \  
\        /  Unit Tests (70%)
 \______/   Fast, focused, high volume
            Test individual components
```

## Test-Driven Development Workflow

### The Red-Green-Refactor Cycle

**Step 1: Define the Behavior (RED)**
- Write a clear behavioral specification in Given-When-Then format
- Implement this as a failing test that defines the expected outcome
- Confirm the test fails for the right reason (missing functionality, not broken test)

**Step 2: Implement Minimally (GREEN)**
- Write the simplest possible code to make the test pass
- Resist the urge to add features not covered by the current test
- Focus solely on satisfying the test requirements

**Step 3: Refactor Confidently (REFACTOR)**
- With tests passing, improve code structure, readability, and performance
- Refactor both application code and test code for maintainability
- Ensure tests continue to pass throughout refactoring process

## Testing Strategy by System Layer

### Unit Testing (The Foundation)
**Scope:** Individual functions, methods, or classes in complete isolation
**Speed:** Milliseconds per test
**Dependencies:** All external dependencies mocked or stubbed

**Key Practices:**
- Test pure functions and business logic extensively
- Mock all external dependencies (databases, APIs, file systems)
- Focus on edge cases, boundary conditions, and error scenarios
- Maintain high test coverage for core business logic

### Integration Testing (The Contracts)
**Scope:** How multiple internal components work together
**Speed:** Seconds per test
**Dependencies:** Mock only external systems, use real internal dependencies

**Key Practices:**
- Test service-to-service interactions within your system
- Validate data transformation pipelines
- Test configuration and dependency injection
- Verify error propagation between components

### End-to-End Testing (The User Journey)
**Scope:** Complete user workflows through the entire system
**Speed:** Minutes per test
**Dependencies:** Minimal mocking, use production-like environment

**Key Practices:**
- Test critical user paths from start to finish
- Validate system behavior under realistic conditions
- Include authentication, authorization, and data persistence
- Test across different user types and scenarios

## Test Organization & Structure

### Directory Structure
```
tests/
├── unit/           # Fast, isolated tests
├── integration/    # Component interaction tests  
├── e2e/           # End-to-end user journey tests
├── fixtures/      # Shared test data and utilities
└── config         # Test configuration and shared utilities
```

### Naming Conventions
- **Test files:** `test_[module_name]` or `[module_name].test` (depending on language conventions)
- **Test functions/methods:** `test_[behavior_being_tested]` or `should_[behavior]_when_[condition]`
- **Test classes/suites:** `[ComponentName]Tests` (when grouping related tests)

### Test Structure Template
```
test_should_[expected_behavior]_when_[conditions]:
    // Given: Set up the initial state
    // Arrange test data and dependencies
    
    // When: Execute the behavior being tested  
    // Act on the system under test
    
    // Then: Verify the expected outcome
    // Assert the results match expectations
```

## Quality Gates & Test Discipline

### Pre-Commit Requirements
- All tests must pass before code can be committed
- New functionality requires corresponding test coverage
- Modified functionality requires updated test coverage
- No commented-out or skipped tests without documented justification

### Test Maintenance Principles
- **Atomic Tests:** Each test validates one specific behavior
- **Independent Tests:** Tests can run in any order without dependencies
- **Repeatable Tests:** Same results every time, regardless of environment
- **Self-Validating:** Clear pass/fail with descriptive assertion messages
- **Timely Tests:** Written alongside or before the production code

### Test Performance Standards
- Unit test suite runs in under 10 seconds
- Integration test suite runs in under 2 minutes  
- Full test suite (including E2E) runs in under 10 minutes
- Tests that exceed these thresholds require optimization or rearchitecting

## Advanced Testing Practices

### Property-Based Testing
Use property-based testing for complex algorithms and data transformations:
- Generate random inputs to test invariant properties
- Particularly valuable for parsing, validation, and mathematical operations
- Helps discover edge cases that example-based tests might miss

### Mutation Testing
Periodically validate test quality by introducing code mutations:
- Ensures tests actually catch bugs when they occur
- Identifies weak or redundant test coverage
- Improves overall test suite effectiveness

### Contract Testing
For systems with multiple services or external dependencies:
- Define and test API contracts explicitly
- Use contract testing tools to verify compatibility
- Maintain backward compatibility through versioned contracts


# CORE DIRECTIVES & OPERATIONAL BOUNDARIES
# These are fundamental, non-negotiable rules that define the correct patterns
# for software construction in this project. Adherence is mandatory.

  - directive: "Handling of Missing Secrets"
    instruction: "When a required secret or API key is not available in the environment, you must immediately halt the operation and explicitly ask the user to provide it. You are required to request real credentials to proceed; never invent, hallucinate, or use placeholder keys for testing connectivity."

  - directive: "Integrity of Environment Files"
    instruction: "The `.env` file is to be treated as a pristine template detailing the required environment variables. You are not permitted to edit this file. All values must be provided by the user in their own environment."


