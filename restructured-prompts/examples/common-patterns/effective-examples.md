# Effective Copilot Prompt Patterns

## Pattern 1: Concrete Code Examples

### ✅ Effective Pattern
```markdown
### Error Handling
Use structured exceptions with context:
```python
class ValidationError(Exception):
    def __init__(self, field: str, value: Any, message: str):
        self.field = field
        self.value = value
        super().__init__(f"Validation failed for {field}: {message}")

def validate_email(email: str) -> str:
    if not email or "@" not in email:
        raise ValidationError("email", email, "Must contain @ symbol")
    return email.lower().strip()
```

### ❌ Ineffective Pattern
```markdown
### Error Handling
Implement proper error handling throughout the application. Errors should be meaningful and provide context to help with debugging. Consider using custom exception classes to categorize different types of errors.
```

**Why the first works better**: <PERSON><PERSON><PERSON> can see the exact pattern and generate similar code structures.

## Pattern 2: Specific Technology Directives

### ✅ Effective Pattern
```markdown
---
applyTo: "**/*.py"
---

# Python Development Guidelines

## HTTP Client: httpx (not requests)
```python
async def fetch_data(url: str) -> dict:
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        response.raise_for_status()
        return response.json()
```

## Testing: pytest with async support
```python
@pytest.mark.asyncio
async def test_should_fetch_user_data():
    # Given
    user_id = "123"
    
    # When
    user = await fetch_user(user_id)
    
    # Then
    assert user.id == user_id
```
```

### ❌ Ineffective Pattern
```markdown
# Development Guidelines
Use modern HTTP clients and testing frameworks. Prefer asynchronous patterns where appropriate.
```

**Why the first works better**: Specific tool recommendations and `applyTo` directives help Copilot understand context.

## Pattern 3: Anti-Pattern Documentation

### ✅ Effective Pattern
```markdown
## Anti-Patterns to Avoid

### ❌ Don't use print() for logging
```python
# Bad
def process_order(order_id):
    print(f"Processing order {order_id}")
    # ... processing logic
    print("Order processed successfully")
```

### ✅ Use structured logging instead
```python
# Good
import logging
logger = logging.getLogger(__name__)

def process_order(order_id: str) -> None:
    logger.info("Processing order", extra={"order_id": order_id})
    # ... processing logic
    logger.info("Order processed successfully", extra={"order_id": order_id})
```
```

### ❌ Ineffective Pattern
```markdown
Don't use print statements for debugging or application output. Use proper logging instead.
```

**Why the first works better**: Shows both the wrong and right way, helping Copilot avoid generating problematic code.

## Pattern 4: Context-Aware Instructions

### ✅ Effective Pattern
```markdown
---
applyTo: "**/test_*.py"
---

# Python Testing Guidelines

## Test Structure (Given-When-Then)
```python
def test_should_calculate_discount_when_coupon_valid():
    # Given
    coupon = Coupon(code="SAVE10", discount=0.1, active=True)
    order_total = 100
    
    # When
    result = calculate_discount(order_total, coupon)
    
    # Then
    assert result == 90
```

## Mocking External Dependencies
```python
def test_should_handle_api_failure(mocker):
    # Given
    mock_client = mocker.patch('httpx.AsyncClient')
    mock_client.return_value.__aenter__.return_value.get.side_effect = httpx.RequestError("Network error")
    
    # When/Then
    with pytest.raises(ExternalServiceError):
        await fetch_external_data("https://api.example.com/data")
```
```

### ❌ Ineffective Pattern
```markdown
# Testing Guidelines
Write comprehensive tests for all functionality. Use mocking for external dependencies.
```

**Why the first works better**: The `applyTo` directive ensures these patterns only apply to test files.

## Pattern 5: Progressive Complexity

### ✅ Effective Pattern
```markdown
## Database Queries

### Simple Query
```python
async def get_user(db: AsyncSession, user_id: int) -> User | None:
    return await db.get(User, user_id)
```

### Query with Filtering
```python
async def get_active_users(db: AsyncSession) -> list[User]:
    result = await db.execute(
        select(User).where(User.is_active == True)
    )
    return result.scalars().all()
```

### Complex Query with Joins
```python
async def get_user_with_orders(db: AsyncSession, user_id: int) -> User | None:
    result = await db.execute(
        select(User)
        .options(selectinload(User.orders))
        .where(User.id == user_id)
    )
    return result.scalar_one_or_none()
```
```

### ❌ Ineffective Pattern
```markdown
Use SQLAlchemy for database operations. Write efficient queries and use proper loading strategies.
```

**Why the first works better**: Shows progression from simple to complex, helping Copilot understand the appropriate level of complexity for different scenarios.

## Pattern 6: Configuration Examples

### ✅ Effective Pattern
```markdown
## Environment Configuration

### Settings Class
```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    database_url: str
    redis_url: str = "redis://localhost:6379"
    secret_key: str
    debug: bool = False
    
    class Config:
        env_file = ".env"

settings = Settings()
```

### Usage in FastAPI
```python
from fastapi import Depends

async def get_settings() -> Settings:
    return settings

@app.get("/health")
async def health_check(settings: Settings = Depends(get_settings)):
    return {"status": "healthy", "debug": settings.debug}
```
```

### ❌ Ineffective Pattern
```markdown
Use environment variables for configuration. Implement proper settings management.
```

**Why the first works better**: Shows the complete pattern including how to use the configuration in the application.

## Key Principles for Effective Patterns

1. **Show, Don't Tell**: Provide concrete code examples rather than abstract descriptions
2. **Use ApplyTo Directives**: Target instructions to specific file types or patterns
3. **Include Anti-Patterns**: Show what NOT to do alongside the correct approach
4. **Progressive Examples**: Start simple and show more complex variations
5. **Complete Context**: Show how patterns fit into the larger application structure
6. **Modern Practices**: Focus on current best practices and up-to-date tooling

## Measuring Effectiveness

Effective patterns should:
- Generate more relevant code suggestions from Copilot
- Reduce the need for manual corrections
- Help developers follow consistent patterns across the codebase
- Provide clear guidance for both common and edge cases
