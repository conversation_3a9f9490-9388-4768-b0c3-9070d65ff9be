---
applyTo: "**/*.py"
---

# Python Development Guidelines

## Technology Stack (2025 Edition)

### Language: Python 3.12+
Leverage latest performance improvements, modern asyncio features, and language enhancements like structural pattern matching.

### Dependency & Environment Management: uv
Extremely fast, modern tool that serves as both package installer and virtual environment manager.

```bash
# Create environment
uv venv

# Activate environment
source .venv/bin/activate

# Add dependency
uv pip install "fastapi"

# Add development dependency
uv pip install "pytest" --group dev
```

### Linting & Formatting: Ruff
All-in-one, extremely fast tool written in Rust. Replaces Flake8, isort, and Black.

### Testing Framework: pytest
Simple, expressive syntax using plain assert statements with powerful fixture system.

### Data Validation: Pydantic
Uses Python's type hints to provide runtime data validation, serialization, and settings management.

### Web Framework: FastAPI
Built on Pydantic and Starlette, provides automatic interactive documentation and best-in-class performance.

## Project Structure: The Modern src Layout

```
project-root/
├── .venv/                     # Virtual environment managed by uv
├── src/                       # Main application source code
│   └── my_project/
│       ├── __init__.py
│       ├── api/               # API endpoints (FastAPI routers)
│       ├── core/              # Core logic, settings, etc.
│       ├── models/            # Pydantic data models
│       └── services/          # Business logic
├── tests/                     # All tests
│   ├── __init__.py
│   ├── test_api.py
│   └── test_services.py
├── .env                       # Environment variables (never committed)
├── pyproject.toml             # Project definition, dependencies, and tool config
└── README.md
```

## Development Guidelines

### Style and Formatting
All code must be formatted using Ruff. Configure pyproject.toml to run `ruff format .` and `ruff check --fix .`.

### Type Hints
All new functions and methods must have 100% type annotation for their arguments and return values.

```python
# Clear, explicit, and tool-friendly
def get_user(user_id: int, db_session: DbSession) -> User | None:
    # ... implementation ...
```

### Asynchronous Code (asyncio)
Use async/await for all I/O-bound operations including database calls, external API requests, and file operations.

```python
async def fetch_user_data(user_id: int) -> UserData:
    async with httpx.AsyncClient() as client:
        response = await client.get(f"/users/{user_id}")
        return UserData.model_validate(response.json())
```

### Structured Logging
Use Python's built-in logging module. Never use print() statements for debugging or application output.

```python
import logging

logger = logging.getLogger(__name__)

def process_order(order_id: str) -> None:
    logger.info("Processing order", extra={"order_id": order_id})
    # ... implementation ...
```

## Testing Doctrine

### Test Runner: pytest
The only test runner to be used.

### Test-Driven Development (TDD)
Follow the "Red-Green-Refactor" cycle for all new features.

### Test Structure
```python
def test_should_calculate_discount_when_coupon_valid():
    # Given
    coupon = Coupon(code="SAVE10", discount=0.1)
    order_total = 100
    
    # When
    result = calculate_discount(order_total, coupon)
    
    # Then
    assert result == 90
```

### Mocking
Use pytest-mock plugin for mocking external dependencies in unit tests.

## Core Directives

### Handling Missing Secrets
When a required secret or API key is not available, immediately halt operation and explicitly ask the user to provide it. Never use placeholder keys.

### Environment Files
The `.env` file is a pristine template detailing required environment variables. Do not edit this file—all values must be provided by the user.
