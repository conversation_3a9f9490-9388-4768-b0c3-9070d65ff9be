# Repository-Level Copilot Instructions

These files contain technology-specific instructions with proper `applyTo` directives for use in individual project repositories. Each file is optimized for specific technology stacks and includes targeted guidance.

## Files in this Directory

- `python-development.md` - Python-specific guidelines with modern tooling (uv, ruff, pytest)
- `flutter-dart-development.md` - Flutter/Dart development with Riverpod and go_router
- `python-testing.md` - Python-specific testing implementations and patterns
- `flutter-testing.md` - Flutter-specific testing strategies and tools
- `operational-protocols.md` - Technology-specific operational procedures

## Usage

### Direct Repository Use
Copy the relevant files to your project's `.github/` directory or incorporate into your `copilot-instructions.md` file.

### Template Customization
Use these files as the basis for creating customized instructions for your specific project needs.

## Characteristics

- **Technology Specific**: Tailored guidance for particular programming languages and frameworks
- **ApplyTo Directives**: Proper file pattern matching for targeted application
- **Concise**: Each file is 50-150 lines for optimal Copilot processing
- **Modern Tooling**: Focuses on current best practices and up-to-date tool chains
- **Practical Examples**: Includes concrete code examples and implementation patterns

## ApplyTo Patterns Used

- `**/*.py` - Python files
- `**/*.dart` - Dart/Flutter files
- `**/test/**` - Test files
- `**` - All files (for general principles)

## Integration Notes

These repository-level instructions are designed to work alongside workspace-level instructions. Copilot will combine guidance from both levels, with repository-level instructions taking precedence for technology-specific concerns.
