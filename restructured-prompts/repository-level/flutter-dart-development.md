---
applyTo: "**/*.dart"
---

# Flutter & Dart Development Guidelines

## Technology Stack

### Dart 3.0+
Utilize modern features like records, patterns, and class modifiers.

### Flutter 3.10+
Cross-platform UI toolkit for building the application frontend.

### Riverpod 2.0+
Primary framework for dependency injection and state management. Prefer this over Provider or BLoC.

### Dio
Primary HTTP client for all external network requests. Prefer over the base `http` package.

### go_router
Declarative routing solution to manage navigation and deep linking.

### Testing: flutter_test / test
Standard frameworks for unit and widget testing.

### Mocking: mocktail
Powerful and type-safe mocking library for tests. Prefer over Mockito.

## Project Structure

```
/lib
  /src
    /features              # Main application features (e.g., auth, settings, dashboard)
      /auth
        /application         # State management logic (Riverpod providers)
        /domain              # Business logic and entities (pure Dart)
        /data                # Data sources (API clients, repositories)
        /presentation        # UI (Widgets, Screens, Components)
          /screens
          /widgets
    /core                  # Shared, cross-cutting concerns
      /api                   # API client setup (e.g., Dio instance)
      /config                # App configuration, themes, constants
      /routing               # GoRouter setup and route definitions
      /utils                 # Utility functions
      /widgets               # Common, shared widgets (e.g., AppButton)
  main.dart                # App entry point and ProviderScope setup
/test
  /features
    /auth
      /application
      /domain
      /data
  # ... (test structure mirrors lib/src)
```

## Development Guidelines

### Dart Best Practices
- Enable and adhere to all rules in the `flutter_lints` package
- Always use `final` for variables that are not reassigned
- Use `const` wherever possible, especially for constructors and widgets to improve performance
- Utilize Dart's null safety features rigorously. Avoid using the `!` (bang) operator
- Leverage pattern matching and records for function returns and destructuring

```dart
// Good: Using records and pattern matching
(String name, int age) getUserInfo(User user) {
  return (user.name, user.age);
}

final (name, age) = getUserInfo(currentUser);
```

### Flutter UI Guidelines
- Build the UI declaratively. State should be managed by Riverpod providers
- Break down complex screens into smaller, reusable widgets
- A widget should ideally fit on one screen and have a single responsibility
- Prefer `const` constructors for widgets to allow Flutter to skip unnecessary rebuilds
- Use `go_router` for all navigation. Avoid using the imperative `Navigator.of(context).push()`

```dart
// Good: Declarative UI with const constructor
class UserCard extends StatelessWidget {
  const UserCard({
    super.key,
    required this.user,
  });

  final User user;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(user.name),
        subtitle: Text(user.email),
      ),
    );
  }
}
```

### State Management with Riverpod
- Use Riverpod for all state management
- For simple state, use `StateProvider`
- For complex logic, use `Notifier` or `AsyncNotifier` from `riverpod_generator`
- Data fetching and business logic must be handled within Riverpod providers, not directly in widget build methods

```dart
// Good: Using AsyncNotifier for complex state
@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  Future<User?> build() async {
    return await ref.read(userRepositoryProvider).getCurrentUser();
  }

  Future<void> updateUser(User user) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return await ref.read(userRepositoryProvider).updateUser(user);
    });
  }
}
```

### API Integration
- Centralize all API calls within Repository classes in the `/data` layer of each feature
- Use `Dio` for network requests. Configure a single `Dio` instance with interceptors
- Model all API responses using pure Dart classes with `fromJson`/`toJson` methods

```dart
// Good: Repository pattern with Dio
class UserRepository {
  const UserRepository(this._dio);
  
  final Dio _dio;

  Future<User> getUser(String id) async {
    final response = await _dio.get('/users/$id');
    return User.fromJson(response.data);
  }
}
```

## Testing Implementation

### Unit Tests (Dart)
- Test a single function, class, or Riverpod Notifier in complete isolation
- All external dependencies must be mocked using `mocktail`
- Location: `/test/**`

### Widget Tests (Flutter)
- Test a single widget in isolation
- Verify UI appearance and interaction using `flutter_test`'s `pumpWidget` and finder methods
- Mock service/provider dependencies

### Integration / E2E Tests (Flutter)
- Test complete user workflow on a real device or emulator
- Location: `/integration_test/`
- The test script drives the app through its UI to verify feature behavior

### Test Organization
- The directory structure within `/test` must mirror the application's structure in `/lib/src`
- All test files must end with the `_test.dart` suffix
- Test descriptions should read like a sentence describing the behavior
