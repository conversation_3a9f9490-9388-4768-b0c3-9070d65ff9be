---
applyTo: "**/test/**/*.dart"
---

# Flutter Testing Implementation

## Testing Pyramid Strategy

### Unit Tests (70%) - Dart
**Tool**: `test` package
**Location**: `/test/**`
**Purpose**: Test a single function, class, or Riverpod Notifier in complete isolation

```dart
import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('UserService', () {
    late UserService userService;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      userService = UserService(mockRepository);
    });

    test('should return user when valid ID provided', () async {
      // Given
      const userId = '123';
      final expectedUser = User(id: userId, name: '<PERSON>');
      when(() => mockRepository.getUser(userId))
          .thenAnswer((_) async => expectedUser);

      // When
      final result = await userService.getUser(userId);

      // Then
      expect(result, equals(expectedUser));
      verify(() => mockRepository.getUser(userId)).called(1);
    });
  });
}
```

### Widget Tests (20%) - Flutter
**Tool**: `flutter_test` package
**Location**: `/test/**`
**Purpose**: Test a single widget in isolation, verify UI appearance and interaction

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

void main() {
  group('UserCard Widget', () {
    testWidgets('should display user name and email', (tester) async {
      // Given
      const user = User(id: '1', name: 'John Doe', email: '<EMAIL>');

      // When
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserCard(user: user),
          ),
        ),
      );

      // Then
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('should call onTap when card is tapped', (tester) async {
      // Given
      const user = User(id: '1', name: 'John Doe', email: '<EMAIL>');
      var tapCalled = false;

      // When
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserCard(
              user: user,
              onTap: () => tapCalled = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(UserCard));
      await tester.pump();

      // Then
      expect(tapCalled, isTrue);
    });
  });
}
```

### Integration/E2E Tests (10%) - Flutter
**Tool**: `integration_test` package
**Location**: `/integration_test/`
**Purpose**: Test complete user workflow on a real device or emulator

```dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:myapp/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('User Authentication Flow', () {
    testWidgets('should complete login flow successfully', (tester) async {
      // Given
      app.main();
      await tester.pumpAndSettle();

      // When - Navigate to login
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Enter credentials
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      
      // Submit login
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle();

      // Then - Verify successful login
      expect(find.text('Welcome'), findsOneWidget);
      expect(find.text('Dashboard'), findsOneWidget);
    });
  });
}
```

## Riverpod Testing

### Testing Providers
```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('UserNotifier', () {
    late ProviderContainer container;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      container = ProviderContainer(
        overrides: [
          userRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should load user data on initialization', () async {
      // Given
      final expectedUser = User(id: '1', name: 'John');
      when(() => mockRepository.getCurrentUser())
          .thenAnswer((_) async => expectedUser);

      // When
      final notifier = container.read(userNotifierProvider.notifier);
      final state = await container.read(userNotifierProvider.future);

      // Then
      expect(state, equals(expectedUser));
    });
  });
}
```

### Testing Widgets with Providers
```dart
Widget createWidgetUnderTest({List<Override> overrides = const []}) {
  return ProviderScope(
    overrides: overrides,
    child: MaterialApp(
      home: Scaffold(
        body: UserProfileScreen(),
      ),
    ),
  );
}

testWidgets('should display loading indicator while fetching user', (tester) async {
  // Given
  when(() => mockRepository.getCurrentUser())
      .thenAnswer((_) async {
    await Future.delayed(const Duration(seconds: 1));
    return User(id: '1', name: 'John');
  });

  // When
  await tester.pumpWidget(
    createWidgetUnderTest(
      overrides: [
        userRepositoryProvider.overrideWithValue(mockRepository),
      ],
    ),
  );

  // Then
  expect(find.byType(CircularProgressIndicator), findsOneWidget);
});
```

## Mocking with Mocktail

### Creating Mocks
```dart
import 'package:mocktail/mocktail.dart';

class MockUserRepository extends Mock implements UserRepository {}
class MockApiClient extends Mock implements ApiClient {}

// For classes with type parameters
class MockFuture<T> extends Mock implements Future<T> {}
```

### Setting Up Fallback Values
```dart
void main() {
  setUpAll(() {
    // Register fallback values for complex types
    registerFallbackValue(User.empty());
    registerFallbackValue(Uri.parse('https://example.com'));
  });

  // ... tests
}
```

## Test Organization

### Directory Structure
```
test/
├── features/
│   ├── auth/
│   │   ├── application/
│   │   │   └── auth_notifier_test.dart
│   │   ├── domain/
│   │   │   └── user_test.dart
│   │   ├── data/
│   │   │   └── auth_repository_test.dart
│   │   └── presentation/
│   │       ├── screens/
│   │       │   └── login_screen_test.dart
│   │       └── widgets/
│   │           └── login_form_test.dart
├── core/
│   ├── utils/
│   │   └── validators_test.dart
│   └── widgets/
│       └── custom_button_test.dart
└── helpers/
    ├── test_helpers.dart
    └── mock_data.dart
```

### Test Naming Conventions
- Test files: `[component_name]_test.dart`
- Test descriptions: `'should [expected behavior] when [condition]'`
- Widget tests: `'should [UI behavior] when [user action or state]'`

### Test Helpers
```dart
// test/helpers/test_helpers.dart
class TestHelpers {
  static Widget wrapWithMaterialApp(Widget child) {
    return MaterialApp(home: Scaffold(body: child));
  }

  static Widget wrapWithProviderScope(
    Widget child, {
    List<Override> overrides = const [],
  }) {
    return ProviderScope(
      overrides: overrides,
      child: wrapWithMaterialApp(child),
    );
  }
}
```

## Performance Testing

### Widget Performance
```dart
testWidgets('should render large list efficiently', (tester) async {
  // Given
  final items = List.generate(1000, (i) => 'Item $i');

  // When
  await tester.pumpWidget(
    MaterialApp(
      home: ListView.builder(
        itemCount: items.length,
        itemBuilder: (context, index) => ListTile(title: Text(items[index])),
      ),
    ),
  );

  // Then - Should complete without timeout
  await tester.pumpAndSettle();
  expect(find.byType(ListView), findsOneWidget);
});
```

## Quality Gates
- All tests must pass before code can be committed
- Widget tests should complete in under 30 seconds
- Integration tests should complete in under 5 minutes
- Test coverage for business logic: minimum 80%
