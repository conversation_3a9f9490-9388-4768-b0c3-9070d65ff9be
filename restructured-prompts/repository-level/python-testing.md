---
applyTo: "**/test_*.py"
---

# Python Testing Implementation

## Testing Framework: pytest

### Test Structure
Use the Given-When-Then pattern for clear test organization:

```python
def test_should_calculate_discount_when_coupon_valid():
    # Given
    coupon = Coupon(code="SAVE10", discount=0.1, active=True)
    order_total = 100
    
    # When
    result = calculate_discount(order_total, coupon)
    
    # Then
    assert result == 90
```

### Test Organization
```
tests/
├── unit/           # Fast, isolated tests
├── integration/    # Component interaction tests  
├── e2e/           # End-to-end user journey tests
├── fixtures/      # Shared test data and utilities
└── conftest.py    # pytest configuration and shared fixtures
```

### Naming Conventions
- Test files: `test_[module_name].py`
- Test functions: `test_should_[behavior]_when_[condition]`
- Test classes: `Test[ComponentName]` (when grouping related tests)

## Mocking with pytest-mock

### External Dependencies
Mock all external dependencies in unit tests:

```python
def test_should_fetch_user_data_when_api_available(mocker):
    # Given
    mock_client = mocker.patch('httpx.AsyncClient')
    mock_response = mocker.Mock()
    mock_response.json.return_value = {"id": 1, "name": "<PERSON>"}
    mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
    
    # When
    result = await fetch_user_data(1)
    
    # Then
    assert result.name == "John"
```

### Database Mocking
Use pytest fixtures for database testing:

```python
@pytest.fixture
async def db_session():
    # Create test database session
    async with async_session() as session:
        yield session
        await session.rollback()

def test_should_create_user_when_valid_data(db_session):
    # Given
    user_data = {"name": "John", "email": "<EMAIL>"}
    
    # When
    user = create_user(db_session, user_data)
    
    # Then
    assert user.name == "John"
    assert user.email == "<EMAIL>"
```

## FastAPI Testing

### Test Client Setup
```python
from fastapi.testclient import TestClient
from myapp.main import app

client = TestClient(app)

def test_should_return_user_when_valid_id():
    # When
    response = client.get("/users/1")
    
    # Then
    assert response.status_code == 200
    assert response.json()["id"] == 1
```

### Dependency Overrides
```python
def test_should_handle_database_error(mocker):
    # Given
    mock_db = mocker.Mock()
    mock_db.get_user.side_effect = DatabaseError("Connection failed")
    
    app.dependency_overrides[get_database] = lambda: mock_db
    
    # When
    response = client.get("/users/1")
    
    # Then
    assert response.status_code == 500
    
    # Cleanup
    app.dependency_overrides.clear()
```

## Async Testing

### Async Test Functions
```python
import pytest

@pytest.mark.asyncio
async def test_should_process_async_operation():
    # Given
    data = {"key": "value"}
    
    # When
    result = await async_process_data(data)
    
    # Then
    assert result.processed is True
```

### Async Fixtures
```python
@pytest.fixture
async def async_client():
    async with httpx.AsyncClient() as client:
        yield client

@pytest.mark.asyncio
async def test_should_make_async_request(async_client):
    # When
    response = await async_client.get("https://api.example.com/data")
    
    # Then
    assert response.status_code == 200
```

## Test Configuration

### conftest.py
```python
import pytest
from unittest.mock import AsyncMock

@pytest.fixture
def mock_database():
    """Mock database for testing."""
    return AsyncMock()

@pytest.fixture(autouse=True)
def reset_environment():
    """Reset environment variables after each test."""
    yield
    # Cleanup code here

# Configure pytest-asyncio
pytest_plugins = ('pytest_asyncio',)
```

### pytest.ini
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=term-missing
    --cov-report=html
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
```

## Performance Testing

### Benchmarking with pytest-benchmark
```python
def test_performance_of_data_processing(benchmark):
    # Given
    large_dataset = generate_test_data(10000)
    
    # When/Then
    result = benchmark(process_data, large_dataset)
    assert len(result) == 10000
```

## Quality Gates
- Unit test suite runs in under 10 seconds
- Integration test suite runs in under 2 minutes  
- All tests must pass before code can be committed
- Test coverage for business logic: minimum 80%
