# General Software Project - Copilot Instructions

## Project Overview
Multi-language software project following universal engineering principles and best practices for maintainable, scalable code.

## Core Development Principles

### Code Quality Standards
- **Readability First**: Code should be optimized for human understanding
- **Consistency**: Maintain consistent patterns, naming, and structure throughout
- **Simplicity**: Prefer simple solutions that solve the actual problem
- **Testability**: Design code to be easily testable with clear dependencies

### Architecture Guidelines
- **Separation of Concerns**: Each module should have a single, well-defined responsibility
- **Loose Coupling**: Minimize dependencies between components
- **High Cohesion**: Related functionality should be grouped together
- **Interface-Based Design**: Depend on abstractions, not concrete implementations

## Universal Coding Standards

### Naming Conventions
```
Variables and Functions: descriptive, verb-noun combinations
Classes and Types: PascalCase, noun-based names
Constants: UPPER_SNAKE_CASE for immutable values
Files and Directories: kebab-case or snake_case consistently
```

### Function Design
- **Single Responsibility**: Each function should do one thing well
- **Pure Functions**: Prefer functions without side effects when possible
- **Clear Inputs/Outputs**: Make function contracts explicit
- **Error Handling**: Handle errors gracefully and provide meaningful messages

### Documentation Standards
```
// Good: Explains the why, not the what
// Calculate discount based on customer tier and purchase history
// to encourage repeat purchases and reward loyalty
function calculateDiscount(customer, order) {
  // Implementation...
}

// Bad: Explains the obvious
// This function calculates discount
function calculateDiscount(customer, order) {
  // Implementation...
}
```

## Testing Philosophy

### Test Structure (Given-When-Then)
Organize all tests using this clear pattern:
```
test('should calculate correct discount when customer is premium') {
  // Given: Set up the initial state
  const customer = { tier: 'premium', purchaseHistory: 5 };
  const order = { total: 100 };
  
  // When: Execute the behavior being tested
  const discount = calculateDiscount(customer, order);
  
  // Then: Verify the expected outcome
  expect(discount).toBe(15);
}
```

### Testing Pyramid
- **Unit Tests (70%)**: Fast, isolated tests of individual components
- **Integration Tests (20%)**: Test component interactions and data flow
- **End-to-End Tests (10%)**: Test complete user workflows

### Test Quality Standards
- Tests should be independent and able to run in any order
- Each test should verify one specific behavior
- Use descriptive test names that explain the expected behavior
- Mock external dependencies to ensure test isolation

## Error Handling Patterns

### Structured Error Handling
```javascript
// Good: Specific error types with context
class ValidationError extends Error {
  constructor(field, value, message) {
    super(`Validation failed for ${field}: ${message}`);
    this.field = field;
    this.value = value;
    this.name = 'ValidationError';
  }
}

// Usage
if (!isValidEmail(email)) {
  throw new ValidationError('email', email, 'Must be a valid email format');
}
```

### Graceful Degradation
- Provide fallback behavior when non-critical features fail
- Log errors with sufficient context for debugging
- Return meaningful error messages to users
- Fail fast for critical errors that cannot be recovered

## Performance Considerations

### Optimization Guidelines
- **Measure First**: Profile before optimizing to identify actual bottlenecks
- **Algorithmic Efficiency**: Choose appropriate data structures and algorithms
- **Resource Management**: Clean up resources (connections, files, memory) properly
- **Caching Strategy**: Cache expensive operations with appropriate invalidation

### Common Performance Patterns
```python
# Good: Lazy loading and caching
@lru_cache(maxsize=128)
def expensive_calculation(input_data):
    # Expensive operation here
    return result

# Good: Batch operations
def process_items_in_batches(items, batch_size=100):
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        process_batch(batch)
```

## Security Best Practices

### Input Validation
- Validate all inputs at system boundaries
- Use allowlists rather than blocklists when possible
- Sanitize data before processing or storage
- Never trust client-side validation alone

### Authentication and Authorization
- Use established libraries for authentication
- Implement principle of least privilege
- Store secrets securely (environment variables, secret managers)
- Log security-relevant events for monitoring

### Data Protection
```python
# Good: Secure password handling
import bcrypt

def hash_password(password: str) -> str:
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
```

## Configuration Management

### Environment-Based Configuration
- Use environment variables for configuration
- Provide sensible defaults for development
- Validate configuration at startup
- Document all required configuration options

### Configuration Structure
```yaml
# config.yml
development:
  database_url: "sqlite:///dev.db"
  debug: true
  log_level: "DEBUG"

production:
  database_url: "${DATABASE_URL}"
  debug: false
  log_level: "INFO"
```

## Logging and Monitoring

### Structured Logging
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "message": "User login successful",
  "user_id": "12345",
  "ip_address": "***********",
  "request_id": "req-abc123"
}
```

### Monitoring Guidelines
- Log all significant business events
- Include correlation IDs for request tracing
- Monitor key performance indicators (response time, error rate)
- Set up alerts for critical failures

## Version Control Best Practices

### Commit Standards
- Write clear, descriptive commit messages
- Make atomic commits that represent single logical changes
- Use conventional commit format when applicable
- Include issue/ticket numbers in commit messages

### Branching Strategy
- Use feature branches for new development
- Keep branches short-lived and focused
- Require code review before merging
- Maintain a clean, linear history when possible

## Anti-Patterns to Avoid

- **God Objects**: Classes or functions that do too many things
- **Magic Numbers**: Hardcoded values without explanation
- **Copy-Paste Programming**: Duplicating code instead of extracting common functionality
- **Premature Optimization**: Optimizing before identifying actual performance issues
- **Tight Coupling**: Components that are difficult to test or modify independently
- **Silent Failures**: Ignoring errors or failing without proper notification
