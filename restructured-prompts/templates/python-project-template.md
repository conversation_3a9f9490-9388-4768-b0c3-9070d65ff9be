# Python Project - Copilot Instructions

## Project Overview
Modern Python application using FastAPI, async/await patterns, and contemporary tooling for high-performance web services.

## Technology Stack
- **Python**: 3.12+ with modern asyncio features
- **Package Management**: uv for fast dependency resolution and virtual environments
- **Web Framework**: FastAPI with automatic OpenAPI documentation
- **Data Validation**: Pydantic v2 for type-safe data models
- **Database**: SQLAlchemy 2.0+ with async support
- **Testing**: pytest with pytest-asyncio for async testing
- **Code Quality**: Ruff for linting and formatting (replaces Black, isort, Flake8)
- **Type Checking**: mypy for static type analysis

## Project Structure
```
src/
├── myproject/
│   ├── api/              # FastAPI routers and endpoints
│   ├── core/             # Settings, dependencies, security
│   ├── models/           # Pydantic models and database schemas
│   ├── services/         # Business logic layer
│   └── utils/            # Utility functions
tests/
├── unit/                 # Fast, isolated tests
├── integration/          # Component interaction tests
└── e2e/                  # End-to-end API tests
```

## Coding Standards

### Type Hints (Required)
All functions must have complete type annotations:
```python
async def get_user(user_id: int, db: AsyncSession) -> User | None:
    result = await db.get(User, user_id)
    return result
```

### Async/Await Patterns
Use async/await for all I/O operations:
```python
async def fetch_external_data(url: str) -> dict[str, Any]:
    async with httpx.AsyncClient() as client:
        response = await client.get(url)
        return response.json()
```

### Error Handling
Use structured exception handling with custom exceptions:
```python
class UserNotFoundError(Exception):
    def __init__(self, user_id: int):
        self.user_id = user_id
        super().__init__(f"User {user_id} not found")

async def get_user_or_raise(user_id: int) -> User:
    user = await get_user(user_id)
    if not user:
        raise UserNotFoundError(user_id)
    return user
```

### Logging
Use structured logging with context:
```python
import logging

logger = logging.getLogger(__name__)

async def process_order(order_id: str) -> None:
    logger.info("Processing order", extra={"order_id": order_id})
    try:
        # ... processing logic
        logger.info("Order processed successfully", extra={"order_id": order_id})
    except Exception as e:
        logger.error("Order processing failed", extra={"order_id": order_id, "error": str(e)})
        raise
```

## Testing Guidelines

### Test Structure (Given-When-Then)
```python
async def test_should_create_user_when_valid_data_provided():
    # Given
    user_data = {"name": "John Doe", "email": "<EMAIL>"}
    
    # When
    user = await create_user(user_data)
    
    # Then
    assert user.name == "John Doe"
    assert user.email == "<EMAIL>"
    assert user.id is not None
```

### Mocking External Dependencies
```python
async def test_should_handle_api_failure(mocker):
    # Given
    mock_client = mocker.patch('httpx.AsyncClient')
    mock_client.return_value.__aenter__.return_value.get.side_effect = httpx.RequestError("Network error")
    
    # When/Then
    with pytest.raises(ExternalServiceError):
        await fetch_external_data("https://api.example.com/data")
```

### Database Testing
```python
@pytest.fixture
async def db_session():
    async with async_session() as session:
        yield session
        await session.rollback()

async def test_should_persist_user_to_database(db_session):
    # Given
    user_data = UserCreate(name="John", email="<EMAIL>")
    
    # When
    user = await create_user(db_session, user_data)
    await db_session.commit()
    
    # Then
    saved_user = await db_session.get(User, user.id)
    assert saved_user.name == "John"
```

## Architecture Patterns

### Repository Pattern
```python
class UserRepository:
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_by_id(self, user_id: int) -> User | None:
        return await self.db.get(User, user_id)
    
    async def create(self, user_data: UserCreate) -> User:
        user = User(**user_data.model_dump())
        self.db.add(user)
        await self.db.flush()
        return user
```

### Service Layer
```python
class UserService:
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo
    
    async def create_user(self, user_data: UserCreate) -> User:
        # Business logic here
        if await self.user_repo.get_by_email(user_data.email):
            raise UserAlreadyExistsError(user_data.email)
        
        return await self.user_repo.create(user_data)
```

## Common Patterns

### FastAPI Dependency Injection
```python
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with async_session() as session:
        yield session

@router.post("/users/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> UserResponse:
    user = await user_service.create_user(db, user_data)
    return UserResponse.model_validate(user)
```

### Configuration Management
```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    database_url: str
    secret_key: str
    debug: bool = False
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## Anti-Patterns to Avoid

- **No print() statements**: Use logging instead
- **No bare except clauses**: Always specify exception types
- **No synchronous I/O in async functions**: Use async libraries
- **No mutable default arguments**: Use None and create inside function
- **No string concatenation for SQL**: Use parameterized queries
- **No hardcoded secrets**: Use environment variables or secret management
