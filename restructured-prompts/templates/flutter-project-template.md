# Flutter Project - Copilot Instructions

## Project Overview
Cross-platform Flutter application using modern Dart features, Riverpod for state management, and clean architecture principles.

## Technology Stack
- **Dart**: 3.0+ with records, patterns, and class modifiers
- **Flutter**: 3.10+ for cross-platform UI development
- **State Management**: Riverpod 2.0+ with riverpod_generator
- **Navigation**: go_router for declarative routing and deep linking
- **HTTP Client**: Dio for network requests with interceptors
- **Testing**: flutter_test, mocktail for type-safe mocking
- **Code Generation**: build_runner, riverpod_generator, json_annotation

## Project Structure
```
lib/
├── src/
│   ├── features/          # Feature-based organization
│   │   └── auth/
│   │       ├── application/    # Riverpod providers and state
│   │       ├── domain/         # Business logic and entities
│   │       ├── data/           # Repositories and data sources
│   │       └── presentation/   # UI widgets and screens
│   └── core/              # Shared utilities and configuration
│       ├── routing/           # GoRouter configuration
│       ├── theme/             # App theming and design tokens
│       └── widgets/           # Reusable UI components
└── main.dart              # App entry point with ProviderScope
```

## Coding Standards

### Dart Best Practices
Enable flutter_lints and use modern Dart features:
```dart
// Use records for multiple return values
(String name, int age) getUserInfo(User user) => (user.name, user.age);

// Pattern matching for data extraction
final (name, age) = getUserInfo(currentUser);

// Const constructors for performance
class UserCard extends StatelessWidget {
  const UserCard({super.key, required this.user});
  final User user;
  // ...
}
```

### State Management with Riverpod
Use Riverpod generators for type-safe providers:
```dart
@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  Future<User?> build() async {
    return ref.read(userRepositoryProvider).getCurrentUser();
  }

  Future<void> updateUser(User user) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return ref.read(userRepositoryProvider).updateUser(user);
    });
  }
}
```

### Widget Composition
Break complex UIs into focused, reusable widgets:
```dart
class UserProfileScreen extends ConsumerWidget {
  const UserProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userNotifierProvider);
    
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: userAsync.when(
        data: (user) => UserProfileContent(user: user),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => ErrorWidget(error: error),
      ),
    );
  }
}
```

### Navigation with GoRouter
Use declarative routing:
```dart
final goRouter = GoRouter(
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const HomeScreen(),
      routes: [
        GoRoute(
          path: '/profile/:userId',
          builder: (context, state) {
            final userId = state.pathParameters['userId']!;
            return UserProfileScreen(userId: userId);
          },
        ),
      ],
    ),
  ],
);

// Navigate using context.go() or context.push()
void navigateToProfile(String userId) {
  context.go('/profile/$userId');
}
```

## Testing Guidelines

### Unit Testing
Test business logic and providers in isolation:
```dart
void main() {
  group('UserNotifier', () {
    late ProviderContainer container;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      container = ProviderContainer(
        overrides: [
          userRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    test('should load user data on initialization', () async {
      // Given
      final expectedUser = User(id: '1', name: 'John');
      when(() => mockRepository.getCurrentUser())
          .thenAnswer((_) async => expectedUser);

      // When
      final state = await container.read(userNotifierProvider.future);

      // Then
      expect(state, equals(expectedUser));
    });
  });
}
```

### Widget Testing
Test UI behavior and user interactions:
```dart
testWidgets('should display user name when data loaded', (tester) async {
  // Given
  const user = User(id: '1', name: 'John Doe');
  
  await tester.pumpWidget(
    ProviderScope(
      overrides: [
        userNotifierProvider.overrideWith((ref) => AsyncValue.data(user)),
      ],
      child: const MaterialApp(home: UserProfileScreen()),
    ),
  );

  // Then
  expect(find.text('John Doe'), findsOneWidget);
});
```

### Integration Testing
Test complete user workflows:
```dart
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('should complete login flow', (tester) async {
    // Given
    app.main();
    await tester.pumpAndSettle();

    // When
    await tester.tap(find.text('Login'));
    await tester.pumpAndSettle();
    
    await tester.enterText(find.byKey(const Key('email')), '<EMAIL>');
    await tester.enterText(find.byKey(const Key('password')), 'password');
    await tester.tap(find.text('Sign In'));
    await tester.pumpAndSettle();

    // Then
    expect(find.text('Welcome'), findsOneWidget);
  });
}
```

## Architecture Patterns

### Repository Pattern
```dart
abstract class UserRepository {
  Future<User?> getCurrentUser();
  Future<User> updateUser(User user);
}

class ApiUserRepository implements UserRepository {
  const ApiUserRepository(this._dio);
  final Dio _dio;

  @override
  Future<User?> getCurrentUser() async {
    try {
      final response = await _dio.get('/user/me');
      return User.fromJson(response.data);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) return null;
      rethrow;
    }
  }
}
```

### Data Models with JSON Serialization
```dart
@JsonSerializable()
class User {
  const User({
    required this.id,
    required this.name,
    required this.email,
  });

  final String id;
  final String name;
  final String email;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
```

## Common Patterns

### Error Handling
```dart
class ApiException implements Exception {
  const ApiException(this.message, this.statusCode);
  final String message;
  final int statusCode;
}

Future<T> handleApiCall<T>(Future<T> Function() apiCall) async {
  try {
    return await apiCall();
  } on DioException catch (e) {
    throw ApiException(
      e.response?.data['message'] ?? 'Unknown error',
      e.response?.statusCode ?? 500,
    );
  }
}
```

### Loading States
```dart
class LoadingButton extends StatelessWidget {
  const LoadingButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.isLoading = false,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      child: isLoading 
        ? const SizedBox(
            height: 16,
            width: 16,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : child,
    );
  }
}
```

### Form Validation
```dart
class EmailValidator {
  static String? validate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }
}
```

## Anti-Patterns to Avoid

- **No setState in StatelessWidget**: Use StatefulWidget or Riverpod for state
- **No business logic in build methods**: Move logic to providers or services
- **No Navigator.push**: Use GoRouter for all navigation
- **No hardcoded strings**: Use localization or constants
- **No deep widget nesting**: Extract widgets into separate classes
- **No synchronous I/O**: Use async/await for all I/O operations
