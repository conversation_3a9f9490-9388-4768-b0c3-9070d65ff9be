# Copilot Instructions Templates

Ready-to-use `.github/copilot-instructions.md` templates for immediate implementation in your projects. Each template is optimized for GitHub Copilot's processing capabilities and stays under 150 lines.

## Available Templates

### `python-project-template.md`
**Use Case**: Python projects using modern tooling (FastAPI, Django, general Python development)
**Features**: 
- Modern Python 3.12+ practices
- uv for dependency management
- ruff for linting and formatting
- pytest for testing
- Type hints and async/await patterns

### `flutter-project-template.md`
**Use Case**: Flutter/Dart mobile and web applications
**Features**:
- Dart 3.0+ with modern features
- Riverpod for state management
- go_router for navigation
- Proper widget testing patterns
- Clean architecture principles

### `general-project-template.md`
**Use Case**: Multi-language projects or when technology stack is not predetermined
**Features**:
- Universal software engineering principles
- Technology-agnostic best practices
- General code quality guidelines
- Universal testing approaches

### `web-project-template.md`
**Use Case**: JavaScript/TypeScript web applications (<PERSON>act, Vue, Angular, Node.js)
**Features**:
- Modern JavaScript/TypeScript practices
- Component-based architecture
- Testing with Jest/Vitest
- Package management with npm/yarn/pnpm

## Usage Instructions

1. **Choose the Right Template**: Select the template that best matches your project's primary technology stack
2. **Copy to Project**: Copy the template content to `.github/copilot-instructions.md` in your project root
3. **Customize**: Modify the template to match your specific project requirements, coding standards, and team preferences
4. **Validate**: Ensure the file stays under 150 lines for optimal Copilot processing

## Customization Guidelines

- **Keep It Focused**: Add only project-specific guidance that differs from general best practices
- **Use Concrete Examples**: Include specific code patterns and naming conventions used in your project
- **Specify Tools**: Mention the exact versions and configurations of tools your project uses
- **Include Anti-Patterns**: Add guidance on what to avoid in your specific context

## Template Structure

Each template follows this structure:
1. **Project Overview** (2-3 lines)
2. **Technology Stack** (5-10 lines)
3. **Coding Standards** (20-30 lines)
4. **Testing Guidelines** (15-25 lines)
5. **Architecture Patterns** (15-25 lines)
6. **Common Patterns** (10-20 lines)
7. **Anti-Patterns** (5-10 lines)
