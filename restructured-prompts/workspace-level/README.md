# Workspace-Level Copilot Instructions

These files contain general development practices and principles that apply across multiple repositories and technology stacks. They are designed to be used at the workspace or organization level to provide consistent guidance regardless of the specific project.

## Files in this Directory

- `core-engineering-principles.md` - Fundamental software engineering principles and values
- `problem-solving-methodology.md` - Systematic approaches to debugging and problem resolution
- `cognitive-framework.md` - Mental models and decision-making frameworks for developers
- `code-quality-principles.md` - Universal code quality standards and architecture guidelines
- `testing-philosophy.md` - Technology-agnostic testing principles and strategies

## Usage

### Personal Workspace Setup
Copy these files to your personal Copilot instructions directory to apply these principles across all your projects.

### Organization-Level Setup
Use these as the foundation for organization-wide coding standards and practices.

### Integration with Repository-Level Instructions
These workspace-level instructions work in conjunction with repository-level instructions. Copilot will combine both sets of guidance when working in specific projects.

## Characteristics

- **Technology Agnostic**: Principles that apply regardless of programming language or framework
- **Concise**: Each file is 50-100 lines for optimal Copilot processing
- **Actionable**: Focused on practical guidance rather than abstract theory
- **Universal**: Applicable across different types of software projects
