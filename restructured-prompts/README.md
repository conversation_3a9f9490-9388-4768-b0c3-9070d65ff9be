# Restructured Co<PERSON><PERSON> Prompts

This directory contains the reorganized and optimized GitHub Copilot prompt engineering content, structured for maximum effectiveness with <PERSON><PERSON><PERSON>'s capabilities.

## Directory Structure

### 📁 workspace-level/
**Purpose**: General development practices and principles that apply across multiple repositories and technology stacks.

**Usage**: Copy these files to your personal workspace or organization-level prompt collections. They provide foundational guidance that enhances <PERSON><PERSON><PERSON>'s understanding of good software engineering practices regardless of the specific technology stack.

**Content**: 
- Core engineering principles and cognitive frameworks
- Universal problem-solving methodologies
- General code quality and architecture principles
- Technology-agnostic testing philosophy

### 📁 repository-level/
**Purpose**: Technology-specific instructions with proper `applyTo` directives for use in individual project repositories.

**Usage**: Copy the relevant files to your project's `.github/` directory or use them as the basis for your `copilot-instructions.md` file. Each file is optimized for specific technology stacks and includes targeted guidance.

**Content**:
- Python-specific development guidelines
- Flutter/Dart project instructions
- Technology-specific testing implementations
- Stack-specific operational protocols

### 📁 templates/
**Purpose**: Ready-to-use `.github/copilot-instructions.md` templates for immediate implementation.

**Usage**: Copy the appropriate template to your project's `.github/copilot-instructions.md` file and customize as needed. Each template is under 150 lines for optimal Copilot processing.

**Available Templates**:
- Python projects (FastAPI, Django, general Python)
- Flutter/Dart projects
- General software development projects
- Multi-language projects

### 📁 examples/
**Purpose**: Concrete examples, before/after comparisons, and common patterns.

**Usage**: Reference these examples to understand effective prompt engineering patterns and see how to apply the principles in practice.

**Content**:
- Before/after prompt improvements
- Common anti-patterns and their solutions
- Real-world implementation examples
- Best practice demonstrations

## Quick Start

1. **For Workspace-Level Setup**: Copy files from `workspace-level/` to your personal or organization prompt collection
2. **For Repository-Level Setup**: Choose the appropriate template from `templates/` and copy to `.github/copilot-instructions.md`
3. **For Customization**: Reference `examples/` for patterns and `repository-level/` for detailed guidance

## Migration from Original Structure

All original content has been preserved in the `archive/` directory. The restructured content maintains the same valuable principles while optimizing for Copilot's specific capabilities and constraints.
